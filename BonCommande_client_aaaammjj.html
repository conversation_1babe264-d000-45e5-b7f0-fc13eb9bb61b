<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="content-type">
    <style type="text/css">@import url(https://themes.googleusercontent.com/fonts/css?kit=fpjTOVmNbO4Lz34iLyptLWuHbT4qV-lhsIVD5LyY_XrrlU6h6snYOEP6A6XGsHFo);

    .lst-kix_list_19-0 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_19-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_14-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_14-3 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_14-0 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_14-4 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_19-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_14-5 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_14-7 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_19-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_19-3 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_14-6 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_17-0 > li {
        counter-increment: lst-ctn-kix_list_17-0
    }

    .lst-kix_list_14-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_19-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_14-8 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_17-2 {
        list-style-type: none
    }

    .lst-kix_list_19-5 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_19-6 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_19-7 > li:before {
        content: "o  "
    }

    .lst-kix_list_5-0 > li:before {
        content: "o  "
    }

    .lst-kix_list_5-3 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_5-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_5-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_5-7 > li:before {
        content: "o  "
    }

    .lst-kix_list_5-6 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_5-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_5-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_5-5 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_6-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_6-3 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_18-0 > li:before {
        content: "-  "
    }

    .lst-kix_list_6-0 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_6-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_18-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_18-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_6-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_6-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_6-5 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_6-7 > li:before {
        content: "o  "
    }

    .lst-kix_list_6-6 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_2-7 > li:before {
        content: "o  "
    }

    .lst-kix_list_7-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_7-6 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_2-5 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_17-5 > li {
        counter-increment: lst-ctn-kix_list_17-5
    }

    .lst-kix_list_7-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_18-6 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_10-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_18-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_18-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_13-7 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_17-6 > li {
        counter-increment: lst-ctn-kix_list_17-6
    }

    .lst-kix_list_7-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_10-7 > li:before {
        content: "o  "
    }

    .lst-kix_list_15-5 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_10-5 > li:before {
        content: "\0025aa   "
    }

    li.li-bullet-1:before {
        /*margin-left: -4.9pt;*/
        white-space: nowrap;
        display: inline-block;
        /*min-width: 4.9pt*/
        position: absolute;
        left: -0.2cm;
        top: 0;
    }

    .lst-kix_list_10-3 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_4-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_15-7 > li:before {
        content: "o  "
    }


    .lst-kix_list_17-7 > li {
        counter-increment: lst-ctn-kix_list_17-7
    }


    .lst-kix_list_9-2 > li:before {
        content: "\0025aa   "
    }


    .lst-kix_list_4-3 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_4-5 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_19-0 {
        list-style-type: none
    }

    .lst-kix_list_15-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_9-0 > li:before {
        content: "\0021e8   "
    }

    .lst-kix_list_15-3 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_19-8 {
        list-style-type: none
    }

    .lst-kix_list_9-6 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_9-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_11-3 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_2-8 {
        list-style-type: none
    }

    .lst-kix_list_12-3 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_11-5 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_12-1 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_2-2 {
        list-style-type: none
    }

    ul.lst-kix_list_2-3 {
        list-style-type: none
    }

    ul.lst-kix_list_2-0 {
        list-style-type: none
    }

    ul.lst-kix_list_2-1 {
        list-style-type: none
    }

    .lst-kix_list_9-8 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_2-6 {
        list-style-type: none
    }

    .lst-kix_list_1-1 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_2-7 {
        list-style-type: none
    }

    .lst-kix_list_11-7 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_2-4 {
        list-style-type: none
    }

    ul.lst-kix_list_2-5 {
        list-style-type: none
    }

    ul.lst-kix_list_10-0 {
        list-style-type: none
    }

    .lst-kix_list_1-3 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_13-3 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_10-8 {
        list-style-type: none
    }

    ul.lst-kix_list_18-0 {
        list-style-type: none
    }

    ul.lst-kix_list_10-7 {
        list-style-type: none
    }

    .lst-kix_list_1-7 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_10-6 {
        list-style-type: none
    }

    ul.lst-kix_list_10-5 {
        list-style-type: none
    }

    ul.lst-kix_list_10-4 {
        list-style-type: none
    }

    ul.lst-kix_list_10-3 {
        list-style-type: none
    }

    .lst-kix_list_1-5 > li:before {
        content: "\0025aa   "
    }

    li.li-bullet-4:before {
        margin-left: -7.1pt;
        white-space: nowrap;
        display: inline-block;
        min-width: 7.1pt
    }

    ul.lst-kix_list_10-2 {
        list-style-type: none
    }

    ul.lst-kix_list_10-1 {
        list-style-type: none
    }

    .lst-kix_list_13-5 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_12-5 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_18-8 {
        list-style-type: none
    }

    ul.lst-kix_list_18-7 {
        list-style-type: none
    }

    ul.lst-kix_list_18-6 {
        list-style-type: none
    }

    ul.lst-kix_list_18-5 {
        list-style-type: none
    }

    .lst-kix_list_12-7 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_18-4 {
        list-style-type: none
    }

    .lst-kix_list_2-1 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_18-3 {
        list-style-type: none
    }

    ul.lst-kix_list_18-2 {
        list-style-type: none
    }

    ul.lst-kix_list_18-1 {
        list-style-type: none
    }

    .lst-kix_list_2-3 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_13-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_3-0 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_5-7 {
        list-style-type: none
    }

    ul.lst-kix_list_5-8 {
        list-style-type: none
    }

    .lst-kix_list_3-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_3-2 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_5-5 {
        list-style-type: none
    }

    ul.lst-kix_list_5-6 {
        list-style-type: none
    }

    .lst-kix_list_8-1 > li:before {
        content: "\0021e8   "
    }

    ol.lst-kix_list_17-4.start {
        counter-reset: lst-ctn-kix_list_17-4 0
    }

    .lst-kix_list_8-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_3-5 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_5-0 {
        list-style-type: none
    }

    .lst-kix_list_3-4 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_5-3 {
        list-style-type: none
    }

    .lst-kix_list_3-3 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_5-4 {
        list-style-type: none
    }

    ul.lst-kix_list_5-1 {
        list-style-type: none
    }

    .lst-kix_list_8-0 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_5-2 {
        list-style-type: none
    }

    .lst-kix_list_8-7 > li:before {
        content: "o  "
    }

    .lst-kix_list_3-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_8-5 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_8-6 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_8-3 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_13-5 {
        list-style-type: none
    }

    ul.lst-kix_list_13-4 {
        list-style-type: none
    }

    ul.lst-kix_list_13-3 {
        list-style-type: none
    }

    .lst-kix_list_3-6 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_13-2 {
        list-style-type: none
    }

    ul.lst-kix_list_13-1 {
        list-style-type: none
    }

    .lst-kix_list_3-7 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_13-0 {
        list-style-type: none
    }

    .lst-kix_list_8-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_11-2 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_13-8 {
        list-style-type: none
    }

    .lst-kix_list_11-1 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_13-7 {
        list-style-type: none
    }

    ul.lst-kix_list_13-6 {
        list-style-type: none
    }

    .lst-kix_list_11-0 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_8-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_16-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_16-7 > li:before {
        content: "o  "
    }

    .lst-kix_list_16-6 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_17-8 > li {
        counter-increment: lst-ctn-kix_list_17-8
    }

    .lst-kix_list_4-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_4-7 > li:before {
        content: "o  "
    }

    .lst-kix_list_17-0 > li:before {
        content: "" counter(lst-ctn-kix_list_17-0, decimal) ". "
    }

    .lst-kix_list_17-1 > li:before {
        content: "-  "
    }

    ul.lst-kix_list_4-8 {
        list-style-type: none
    }

    .lst-kix_list_16-0 > li:before {
        content: "-  "
    }

    ul.lst-kix_list_4-6 {
        list-style-type: none
    }

    .lst-kix_list_16-1 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_4-7 {
        list-style-type: none
    }

    .lst-kix_list_16-2 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_4-0 {
        list-style-type: none
    }

    .lst-kix_list_16-4 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_4-1 {
        list-style-type: none
    }

    .lst-kix_list_16-3 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_16-5 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_4-4 {
        list-style-type: none
    }

    ul.lst-kix_list_4-5 {
        list-style-type: none
    }

    ul.lst-kix_list_4-2 {
        list-style-type: none
    }

    ul.lst-kix_list_4-3 {
        list-style-type: none
    }

    ul.lst-kix_list_12-6 {
        list-style-type: none
    }

    ul.lst-kix_list_12-5 {
        list-style-type: none
    }

    .lst-kix_list_17-7 > li:before {
        content: "" counter(lst-ctn-kix_list_17-7, lower-latin) ". "
    }

    ul.lst-kix_list_12-4 {
        list-style-type: none
    }

    ul.lst-kix_list_12-3 {
        list-style-type: none
    }

    ul.lst-kix_list_12-2 {
        list-style-type: none
    }

    ul.lst-kix_list_12-1 {
        list-style-type: none
    }

    .lst-kix_list_17-8 > li:before {
        content: "" counter(lst-ctn-kix_list_17-8, lower-roman) ". "
    }

    ul.lst-kix_list_12-0 {
        list-style-type: none
    }

    .lst-kix_list_17-3 > li:before {
        content: "" counter(lst-ctn-kix_list_17-3, decimal) ". "
    }

    .lst-kix_list_17-2 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_17-4 > li:before {
        content: "" counter(lst-ctn-kix_list_17-4, lower-latin) ". "
    }

    ul.lst-kix_list_12-8 {
        list-style-type: none
    }

    ul.lst-kix_list_12-7 {
        list-style-type: none
    }

    ol.lst-kix_list_17-3.start {
        counter-reset: lst-ctn-kix_list_17-3 0
    }

    .lst-kix_list_17-6 > li:before {
        content: "" counter(lst-ctn-kix_list_17-6, decimal) ". "
    }

    .lst-kix_list_7-0 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_17-5 > li:before {
        content: "" counter(lst-ctn-kix_list_17-5, lower-roman) ". "
    }

    .lst-kix_list_2-6 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_2-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_2-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_7-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_7-5 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_7-3 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_7-5 {
        list-style-type: none
    }

    .lst-kix_list_10-0 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_7-6 {
        list-style-type: none
    }

    .lst-kix_list_18-5 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_7-3 {
        list-style-type: none
    }

    ul.lst-kix_list_7-4 {
        list-style-type: none
    }

    .lst-kix_list_13-6 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_13-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_18-3 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_18-7 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_7-7 {
        list-style-type: none
    }

    ul.lst-kix_list_7-8 {
        list-style-type: none
    }

    ul.lst-kix_list_7-1 {
        list-style-type: none
    }

    ul.lst-kix_list_7-2 {
        list-style-type: none
    }

    ul.lst-kix_list_7-0 {
        list-style-type: none
    }

    .lst-kix_list_7-7 > li:before {
        content: "o  "
    }

    .lst-kix_list_15-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_15-6 > li:before {
        content: "\0025cf   "
    }

    ol.lst-kix_list_17-8.start {
        counter-reset: lst-ctn-kix_list_17-8 0
    }

    .lst-kix_list_10-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_10-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_4-0 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_15-3 {
        list-style-type: none
    }

    ul.lst-kix_list_15-2 {
        list-style-type: none
    }

    .lst-kix_list_15-0 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_15-1 {
        list-style-type: none
    }

    .lst-kix_list_15-8 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_15-0 {
        list-style-type: none
    }

    li.li-bullet-3:before {
        margin-left: -7.1pt;
        white-space: nowrap;
        display: inline-block;
        min-width: 7.1pt
    }

    .lst-kix_list_10-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_4-4 > li:before {
        content: "o  "
    }

    ol.lst-kix_list_17-5.start {
        counter-reset: lst-ctn-kix_list_17-5 0
    }

    ul.lst-kix_list_15-8 {
        list-style-type: none
    }

    .lst-kix_list_4-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_4-6 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_15-7 {
        list-style-type: none
    }

    .lst-kix_list_17-4 > li {
        counter-increment: lst-ctn-kix_list_17-4
    }

    ul.lst-kix_list_15-6 {
        list-style-type: none
    }

    .lst-kix_list_9-3 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_15-5 {
        list-style-type: none
    }

    ul.lst-kix_list_15-4 {
        list-style-type: none
    }

    .lst-kix_list_15-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_10-6 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_9-1 > li:before {
        content: "o  "
    }

    .lst-kix_list_9-7 > li:before {
        content: "o  "
    }

    .lst-kix_list_11-4 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_12-4 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_9-5 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_6-6 {
        list-style-type: none
    }

    ul.lst-kix_list_6-7 {
        list-style-type: none
    }

    ul.lst-kix_list_6-4 {
        list-style-type: none
    }

    ul.lst-kix_list_6-5 {
        list-style-type: none
    }

    ul.lst-kix_list_6-8 {
        list-style-type: none
    }

    .lst-kix_list_12-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_11-6 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_1-0 > li:before {
        content: "o  "
    }

    ul.lst-kix_list_6-2 {
        list-style-type: none
    }

    .lst-kix_list_11-8 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_6-3 {
        list-style-type: none
    }

    .lst-kix_list_1-2 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_6-0 {
        list-style-type: none
    }

    .lst-kix_list_12-0 > li:before {
        content: "\0025cf   "
    }

    .lst-kix_list_17-3 > li {
        counter-increment: lst-ctn-kix_list_17-3
    }

    ul.lst-kix_list_6-1 {
        list-style-type: none
    }

    .lst-kix_list_1-4 > li:before {
        content: "o  "
    }

    .lst-kix_list_13-0 > li:before {
        content: "\0025cf   "
    }

    ul.lst-kix_list_14-4 {
        list-style-type: none
    }

    ul.lst-kix_list_14-3 {
        list-style-type: none
    }

    ul.lst-kix_list_14-2 {
        list-style-type: none
    }

    .lst-kix_list_13-4 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_14-1 {
        list-style-type: none
    }

    ol.lst-kix_list_17-6.start {
        counter-reset: lst-ctn-kix_list_17-6 0
    }

    ul.lst-kix_list_14-0 {
        list-style-type: none
    }

    .lst-kix_list_1-6 > li:before {
        content: "\0025cf   "
    }

    li.li-bullet-0:before {
        margin-left: -18pt;
        white-space: nowrap;
        display: inline-block;
        min-width: 18pt
    }

    ul.lst-kix_list_14-8 {
        list-style-type: none
    }

    ul.lst-kix_list_14-7 {
        list-style-type: none
    }

    .lst-kix_list_2-0 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_12-6 > li:before {
        content: "\0025aa   "
    }

    ul.lst-kix_list_14-6 {
        list-style-type: none
    }

    ul.lst-kix_list_14-5 {
        list-style-type: none
    }

    .lst-kix_list_1-8 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_2-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_13-2 > li:before {
        content: "\0025aa   "
    }

    .lst-kix_list_12-8 > li:before {
        content: "\0025aa   "
    }

    ol {
        margin: 0;
        padding: 0
    }

    table td, table th {
        padding: 0
    }

    .c32 {
        -webkit-text-decoration-skip: none;
        color: #000000;
        font-weight: 700;
        text-decoration: underline;
        vertical-align: baseline;
        text-decoration-skip-ink: none;
        font-size: 9pt;
        font-family: "Verdana";
        font-style: normal
    }

    .c7 {
        padding-top: 12pt;
        border-bottom-color: #36bcb6;
        border-bottom-width: 0.5pt;
        padding-bottom: 1pt;
        line-height: 1.0;
        border-bottom-style: dotted;
        orphans: 2;
        widows: 2;
        text-align: left
    }

    .c20 {
        background-color: #00ff00;
        color: #36bcb6;
        font-weight: 400;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 16pt;
        font-family: "Montserrat";
        font-style: normal
    }

    .c19 {
        color: #ffffff;
        font-weight: 400;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 14pt;
        font-family: "Arial";
        font-style: normal
    }

    .c0 {
        color: #000000;
        font-weight: 400;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 12px;
        font-family: "Calibri";
        font-style: normal
    }

    .c12 {
        color: #000000;
        font-weight: 400;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 9pt;
        font-family: "Arial";
        font-style: normal
    }

    .c16 {
        -webkit-text-decoration-skip: none;
        color: #000000;
        font-weight: 400;
        text-decoration: underline;
        text-decoration-skip-ink: none;
        font-size: 9pt;
        font-family: "Arial"
    }

    .c48 {
        -webkit-text-decoration-skip: none;
        color: #000000;
        font-weight: 700;
        text-decoration: underline;
        text-decoration-skip-ink: none;
        font-size: 10pt;
        font-family: "Arial"
    }

    .c47 {
        -webkit-text-decoration-skip: none;
        color: #000000;
        font-weight: 700;
        text-decoration: underline;
        text-decoration-skip-ink: none;
        font-size: 9pt;
        font-family: "Arial"
    }

    .c3 {
        color: #000000;
        font-weight: 700;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 9pt;
        font-family: "Calibri";
        font-style: normal;
        margin-left: 0.7cm;
    }

    .c24 {
        color: #000000;
        font-weight: 400;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 11pt;
        font-family: "Arial";
        font-style: normal
    }

    .c22 {
        color: #000000;
        font-weight: 400;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 12pt;
        font-family: "Verdana";
        font-style: normal
    }

    .c30 {
        color: #000000;
        font-weight: 700;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 20pt;
        font-family: "Arial";
        font-style: normal
    }

    .c2 {
        color: #000000;
        font-weight: 400;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 10pt;
        font-family: "Verdana";
        font-style: normal
    }

    .c31 {
        color: #36bcb6;
        font-weight: 400;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 14pt;
        font-family: "Arial";
        font-style: normal
    }

    .c8 {
        color: #000000;
        font-weight: 400;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 9pt;
        font-family: "Verdana";
        font-style: normal
    }

    .c46 {
        padding-top: 0pt;
        padding-bottom: 0pt;
        line-height: 1.5;
        orphans: 2;
        widows: 2;
        text-align: center;
        margin-right: 7pt
    }

    .c21 {
        color: #000000;
        font-weight: 700;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 15pt;
        font-family: "Verdana";
        font-style: normal
    }

    .c15 {
        color: #000000;
        font-weight: 400;
        text-decoration: none;
        vertical-align: baseline;
        font-size: 10pt;
        font-family: "Arial";
        font-style: normal
    }

    .c54 {
        padding-top: 0pt;
        padding-bottom: 6pt;
        line-height: 1.0;
        orphans: 2;
        widows: 2;
        text-align: left;
        margin-top: 1.7cm;
        color: white;
    }

    .c17 {
        padding-top: 0pt;
        padding-bottom: 0pt;
        line-height: 1.0;
        orphans: 2;
        widows: 2;
        text-align: left
    }

    .c6 {
        padding-top: 0pt;
        padding-bottom: 11pt;
        line-height: 1.0;
        orphans: 2;
        widows: 2;
        text-align: left
    }

    .c1 {
        padding-top: 0pt;
        padding-bottom: 0pt;
        line-height: 1.0;
        orphans: 2;
        widows: 2;
        text-align: justify
    }

    .c38 {
        padding-top: 0pt;
        padding-bottom: 7pt;
        line-height: 1.5;
        orphans: 2;
        widows: 2;
        text-align: left
    }

    .c55 {
        padding-top: 0pt;
        padding-bottom: 0pt;
        line-height: 1.0;
        orphans: 2;
        widows: 2;
        text-align: center
    }

    .c41 {
        color: #000000;
        font-weight: 700;
        font-size: 10pt;
        font-family: "Verdana"
    }

    .c42 {
        color: #ffffff;
        font-weight: 400;
        font-size: 12pt;
        font-family: "Arial"
    }

    .c56 {
        -webkit-text-decoration-skip: none;
        color: #0000ff;
        text-decoration: underline;
        text-decoration-skip-ink: none
    }

    .c33 {
        font-size: 9pt;
        font-family: "Arial";
        color: #000000;
        font-weight: 400
    }

    .c40 {
        -webkit-text-decoration-skip: none;
        text-decoration: underline;
        text-decoration-skip-ink: none;
        font-style: italic
    }

    .c28 {
        font-size: 10pt;
        font-family: "Arial";
        color: #bfbfbf;
        font-weight: 400
    }

    .c49 {
        color: #ffffff;
        font-weight: 700;
        font-size: 12pt;
        font-family: "Arial"
    }

    .c52 {
        font-size: 10pt;
        font-family: "Arial";
        color: #000000;
        font-weight: 400
    }

    .c53 {
        color: #ffffff;
        font-weight: 700;
        font-size: 14pt;
        font-family: "Arial"
    }

    .c50 {
        color: #36bcb6;
        font-weight: 400;
        font-size: 12pt;
        font-family: "Arial"
    }

    .c23 {
        font-size: 9pt;
        font-weight: 400;
        font-family: "Calibri"
    }

    .c35 {
        font-size: 9pt;
        font-weight: 400;
        font-family: "Arial"
    }

    .c43 {
        font-size: 24pt;
        font-weight: 400;
        font-family: "Arial"
    }

    .c18 {
        font-size: 9pt;
        font-weight: 700;
        font-family: "Calibri"
    }

    .c36 {
        font-size: 11pt;
        font-weight: 400;
        font-family: "Arial"
    }

    .c44 {
        background-color: #ffffff;
        max-width: 595pt; /* Largeur A4 complète */
        width: 100%;
        margin: 0 auto;
        padding: 36pt 36pt 36pt 36pt; /* Marges réduites pour plus d'espace */
        box-sizing: border-box;
    }

    .c14 {
        margin-left: 36pt;
        padding-left: 0pt
    }

    .c29 {
        color: inherit;
        text-decoration: inherit
    }

    .c5 {
        margin-left: 9pt;
        height: 12pt
    }

    .c25 {
        margin-left: 1cm;
        position: relative;
    }

    .c10 {
        padding: 0;
        margin: 0
    }

    .c39 {
        vertical-align: baseline;
        font-style: italic
    }

    .c34 {
        vertical-align: baseline;
        font-style: normal
    }

    .c51 {
        margin-left: 25.1pt;
        padding-left: -10.9pt
    }

    .c37 {
        text-decoration: none
    }

    .c9 {
        height: 12pt
    }

    .c13 {
        margin-right: 56.7pt
    }

    .c11 {
        text-indent: 21.3pt
    }

    .c26 {
        height: 14pt
    }

    .c27 {
        margin-left: 212.4pt
    }

    .c45 {
        page-break-after: avoid
    }

    /* Styles pour les conditions générales en deux colonnes */
    .conditions-container {
        display: flex;
        gap: 30px;
        margin: 20px 0;
        width: 100%;
    }

    .conditions-column {
        flex: 1;
        padding: 0 15px;
        min-width: 0; /* Permet aux colonnes de se rétrécir si nécessaire */
    }

    .conditions-column-left {
        border-right: 1px solid #ccc;
        padding-right: 25px;
    }

    .conditions-title {
        text-align: center;
        font-weight: bold;
        margin-bottom: 20px;
        font-size: 12pt;
        color: #000000;
    }

    /* Styles simplifiés pour la numérotation des conditions générales */

    /* Styles pour l'impression PDF A4 */
    @media print {
        .c44 {
            max-width: none;
            width: 100%;
            margin: 0;
            padding: 20pt;
        }

        .entete {
            max-width: none;
            width: 100%;
            margin: 0;
            padding: 1cm 20pt;
        }

        .conditions-container {
            page-break-inside: avoid;
            gap: 25px;
        }

        .conditions-column {
            padding: 0 10px;
        }

        .conditions-column-left {
            padding-right: 20px;
        }
    }

    /* Styles pour l'affichage écran avec largeur A4 */
    @media screen {
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }

        .c44 {
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: white;
        }
    }

    /* Styles pour le bloc bon de commande */
    .bloc-commande {
        max-width: 595pt;
        height: 113px;
        width: 655px;
        margin: 1.2cm auto;
        background-image: url('images/image8.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        padding: 0pt 20pt;
        box-sizing: border-box;
        position: relative;
    }

    .bloc-commande .date-lieu {
        color: #36bcb6;
        font-size: 14pt;
        font-family: "Arial", sans-serif;
        font-weight: normal;
        margin-bottom: 10px;
        text-align: left;
    }

    .bloc-commande .titre-commande {
        color: #000000;
        font-size: 24pt;
        font-family: "Arial", sans-serif;
        font-weight: bold;
        text-align: center;
        margin: 0;
    }

    .bloc-commande-2 {
        max-width: 595pt;
        width: 656px;
        height: 104px;
        margin: 1.2cm auto;
        background-image: url('images/image9.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        padding: 0pt 20pt;
        box-sizing: border-box;
        position: relative;
    }

    .bloc-commande-2 .date-lieu {
        color: #36bcb6;
        font-size: 14pt;
        font-family: "Arial", sans-serif;
        font-weight: normal;
        margin-bottom: 10px;
        text-align: left;
    }

    .bloc-commande-2 .titre-commande {
        color: #000000;
        font-size: 24pt;
        font-family: "Arial", sans-serif;
        font-weight: bold;
        text-align: center;
        margin: 0;
    }

    .title {
        padding-top: 24pt;
        color: #000000;
        font-weight: 700;
        font-size: 36pt;
        padding-bottom: 6pt;
        font-family: "Times New Roman";
        line-height: 1.0;
        page-break-after: avoid;
        orphans: 2;
        widows: 2;
        text-align: left
    }

    .subtitle {
        padding-top: 18pt;
        color: #666666;
        font-size: 24pt;
        padding-bottom: 4pt;
        font-family: "Georgia";
        line-height: 1.0;
        page-break-after: avoid;
        font-style: italic;
        orphans: 2;
        widows: 2;
        text-align: left
    }

    li {
        color: #000000;
        font-size: 8pt;
        font-family: "Times New Roman"
    }

    p {
        margin: 0;
        color: #000000;
        font-size: 12pt;
        font-family: "Times New Roman"
    }

    h2 {
        padding-top: 0pt;
        margin-bottom: 0;
        font-size: 14pt;
        padding-bottom: 0pt;
        font-weight: bold;
        text-align: center;
    }

    h3 {
        padding-top: 12pt;
        color: #36bcb6;
        border-bottom-color: #36bcb6;
        border-bottom-width: 0.5pt;
        font-size: 12pt;
        padding-bottom: 1pt;
        font-family: "Montserrat";
        line-height: 1.0;
        border-bottom-style: dotted;
        orphans: 2;
        widows: 2;
        text-align: left
    }

    .subh2 {
        font-size: 10pt;
        line-height: 1.0;
        text-align: center;
        margin-bottom: 0.5cm;
    }


    .entete {
        max-width: 595pt; /* Même largeur que le body */
        width: 100%;
        height: 7cm;
        margin: 0 auto; /* Centrer comme le body */
        padding: 1cm 36pt; /* Mêmes marges que le body */
        background-color: #00225d;
        color: white;
        text-align: center;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-content: space-between;
        box-sizing: border-box; /* Inclure le padding dans la largeur */
    }

    .page-break {
        break-after: page;
    }
    </style>
</head>
<body class="c44 doc-content">
<div class="entete">
    <div class="col-gauche">
            <span style="overflow: hidden; display: inline-block; margin: 0.00px 0.00px; border: 0.00px solid #000000; transform: rotate(0.00rad) translateZ(0px); -webkit-transform: rotate(0.00rad) translateZ(0px); width: 275.00px; height: 65.80px;"><img
                    alt="X:\documents\logos\Alienor_blanc.png" src="images/image6.png"
                    style="width: 275.00px; height: 65.80px; margin-left: -0.00px; margin-top: -0.00px; transform: rotate(0.00rad) translateZ(0px); -webkit-transform: rotate(0.00rad) translateZ(0px);"
                    title=""></span>
        <p class="c54">
            <span class="c34 c37 c49">DOSSIER SUIVI PAR</span><span class="c34 c37 c42">&nbsp;:</span><br>
            <span class="c34 c37 c49">Chef de projet : </span>{{ chefDeProjet }}<br>
        </p>
    </div>
    <div class="col-droite">
        Référence : {{ referenceDuDevis }}
    </div>
</div>

<!-- Nouveau bloc bon de commande -->
<div class="bloc-commande">
    <div class="date-lieu">Le Bouscat, {{ date }}</div>
    <div class="titre-commande">Bon de commande</div>
    <div class="titre-commande">Site {{ client }}</div>
</div>

<h3 class="c7"><span class="c31">Fonctionnalités : </span></h3>
<p class="c1"><span class="c24">{{ description }}</span></p>
<p class="c1 c9"><span class="c24"></span></p>
<h3 class="c7"><span class="c31">Co&ucirc;t de mise en place : {{ montantDevisExterneHT }} &euro; HT</span></h3><a
        id="id.txcyh4c7i1ou"></a>
<p class="c17"><span class="c24">Comprend le graphisme, l'intégration et le développement.</span>
</p>
<p class="c17"><span class="c24">Mise en place selon planning disponible au moment de la signature.</span></p>
<p class="c17 c9"><span class="c33 c37 c39"></span></p>
<h3 class="c7"><span class="c50 c34 c37">Facturation</span></h3>
<p class="c1"><span class="c36">{{ facturation }}</span></p>
<h3 class="c7"><span class="c34 c37 c50">Règlement </span></h3>
<p class="c1"><span class="c24">À la réception de la facture </span></p>

<span class="page-break"></span>

<div class="bloc-commande-2">
    <img class="date-lieu" alt="" src="images/image1.png" width="161" height="38" title="">
    <div class="titre-commande">Bon de commande {{ referenceDuDevis }}</div>
</div>

<h3 class="c7"><span class="c31">site {{site}} : {{ montantDevisExterneHT }} &euro; HT</span></h3>
<p class="c17"><span class="c12">TVA 20%&nbsp;: {{ TVA }} &euro;</span></p>
<p class="c17 c9"><span class="c12"></span></p>
<p class="c17"><span class="c33">Total Charge comprise : {{ montantDevisExterne</span><span class="c35">TTC</span><span
        class="c12">&nbsp;}} &euro; TTC</span></p>
<p class="c17 c9"><span class="c34 c48"></span></p>
<p class="c6"><span class="c15">Mention manuscrite &laquo; Bon pour accord &raquo; :</span></p>
<p class="c6"><span class="c28">&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;</span><span
        class="c15">&nbsp;</span></p>
<p class="c6"><span class="c15">Nom et prénom du signataire :</span></p>
<p class="c6"><span class="c28">&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;..&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;..</span><span
        class="c15">&nbsp;</span></p>
<p class="c6"><span class="c15">Adresse email :</span></p>
<p class="c6"><span class="c28">&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;.&hellip;&hellip;&hellip;&hellip;..&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;..</span><span
        class="c15">&nbsp;</span></p>
<p class="c6"><span class="c15">Fonction :</span></p>
<p class="c6"><span class="c28">&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;..&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip; </span>
</p>
<p class="c6 c9"><span class="c15"></span></p>
<p class="c6"><span class="c52">Date : &nbsp; </span><span class="c28">&hellip;&hellip;&hellip; /</span><span
        class="c52">&nbsp;</span><span class="c28 c34 c37">&hellip;&hellip;&hellip; /&hellip;&hellip;&hellip;&hellip;&hellip;.. </span>
</p>
<p class="c17"><span class="c43">&#9633;</span><span class="c36">&nbsp;</span><span class="c16">Je reconnais avoir pris connaissance des conditions générales de vente ci-incluses</span><span
        class="c47">&nbsp;</span><span class="c16 c34">que j'accepte intégralement.</span></p>
<p class="c17 c9"><span class="c16 c34"></span></p>
<p class="c17 c9"><span class="c24"></span></p>
<p class="c6 c9"><span class="c15"></span></p>
<p class="c6 c13"><span class="c52">Signature du client et cachet commercial</span></p>

<span class="page-break"></span>


<h2>CONDITIONS GÉNÉRALES DE VENTE ALIENOR.NET</h2>
<div class="subh2">Mises à jour le 8 août 2019</div>

<p class="c1"><span class="c0">Les conditions générales de vente de la société ALIENOR.NET s'appliquent à toutes les prestations réalisées et matériel vendu par la société ALIENOR.NET.
    Toute commande passée auprès de la société ALIENOR.NET implique donc l'adhésion sans réserve du client aux présentes conditions générales de vente.</span>
</p>

<div class="conditions-container">
    <div class="conditions-column conditions-column-left">
        <p class="c1"><span class="c3">1. Commande</span></p>
        <p class="c1"><span class="c0">La commande n'est prise en charge qu'à compter du retour du bon de commande et des conditions générales de vente à la société</span>
        </p>
        <p class="c1"><span class="c0">ALIENOR.NET, signés et datés par le client.</span></p>
        <p class="c1"><span class="c0">La proposition commerciale formulée dans le bon de commande sera valable 30 jours calendaires maximum, à compter de son émission par la société ALIENOR.NET.</span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">2. Annulation</span></p>
        <p class="c1"><span class="c0">Aucune annulation de commande ne sera prise en compte après retour du bon de commande signé.</span>
        </p>
        <p class="c1"><span class="c0">Toute prestation et/ou matériel ayant fait l'objet d'un bon de commande retourné signé à la société ALIENOR.NET sera du dans son intégralité. </span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">3. Collaboration des parties</span></p>
        <p class="c1"><span class="c0">La société ALIENOR.NET et le client reconnaissent l'importance de leur collaboration effective afin de mener à bien le projet objet de la commande. Ils s'engagent à faire leurs meilleurs efforts pour faciliter l'échange d'informations nécessaires à sa réalisation.</span>
        </p>
        <p class="c1"><span class="c0">Le client fournira au personnel de la société ALIENOR.NET tous les documents, renseignements, éléments existants nécessaires à la bonne compréhension du problème posé ainsi que tous les documents, renseignements, éléments existants nécessaires à la réalisation de la prestation objet de la commande et/ou à la vente de matériel.</span>
        </p>
        <p class="c1"><span class="c0">Pour ce faire, le client désignera un interlocuteur compétent qui le représentera auprès de la société ALIENOR.NET.</span>
        </p>
        <p class="c1"><span class="c0">En cas de retard de fourniture ou de non fourniture des éléments de la part du client, la société ALIENOR.NET procèdera à des rappels, si besoin est par lettre recommandée avec accusé de réception.</span>
        </p>
        <p class="c1"><span class="c0">Suite à ces rappels restés infructueux, le prix de la prestation sera exigible à la date de cl&ocirc;ture du dossier prévue au bon de commande.</span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">4. Prix</span></p>
        <p class="c1"><span class="c0">Les prix des prestations réalisées et/ou du matériel vendu sont ceux en vigueur au jour de la prise de commande.</span>
        </p>
        <p class="c1"><span class="c0">Ils sont libellés en euros et calculés hors taxes.</span></p>
        <p class="c1"><span class="c0">La société ALIENOR.NET s'engage à facturer le matériel et/ou les prestations commandées aux prix indiqués lors de l'enregistrement de la commande.</span>
        </p>
        <p class="c1"><span class="c0">Toutefois, elle s'accorde le droit de modifier ses tarifs à tout moment et en informera le client dans les meilleurs délais.</span>
        </p>
        <p class="c1"><span class="c0">Tout frais bancaire inhérent au paiement des prestations et/ou du matériel par le client sera à la charge exclusive de celui-ci.</span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">5. Escompte</span></p>
        <p class="c1"><span class="c0">Aucun escompte ne sera consenti en cas de paiement anticipé.</span></p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">6. Modalités et délais de paiement</span></p>
        <p class="c1"><span class="c0">Le règlement des commandes s'effectue&nbsp;:</span></p>
        <ul class="c10 lst-kix_list_17-2 start">
            <li class="c1 c25 li-bullet-1"><span class="c0">Soit par chèque</span></li>
            <li class="c1 c25 li-bullet-1"><span class="c0">Soit par virement</span></li>
            <li class="c1 c25 li-bullet-1"><span class="c0">Soit par lettre de change acceptée ou non acceptée</span></li>
            <li class="c1 c25 li-bullet-1"><span class="c0">Soit par prélèvement&nbsp;</span></li>
        </ul>
        <p class="c1"><span class="c0">Les modalités de paiement que devra respecter le client sont celles prévues sur le bon de commande.</span>
        </p>
        <p class="c1"><span class="c0">A défaut de conditions prévues au bon de commande, le client devra verser un acompte de 40% lors de la commande</span>
        </p>
        <p class="c1"><span class="c0">Le solde devra être payé dans sa totalité 30 jours maximum à compter de la date d'émission de la facture. </span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
    </div>

    <div class="conditions-column">
        <p class="c1"><span class="c3">7. Retard de paiement</span></p>
        <p class="c1"><span class="c0">Conformément à l'article L 441-10 du Code de Commerce, des pénalités de retard seront dues à défaut de règlement le jour suivant la date de paiement qui figure sur la facture. En cas de non-respect des conditions prévues au bon de commande et en particulier, en cas de non-paiement par le client des sommes dues à la date d'exigibilité, des pénalités de retard seront appliquées sans qu'un rappel de paiement ne soit nécessaire.</span>
        </p>
        <p class="c1"><span class="c0">Ces pénalités seront calculées au taux d'intérêt appliqué par la Banque Centrale Européenne à son opération de refinancement la plus récente majoré de 10 points de pourcentage et seront exigibles à compter du premier jour de retard de paiement.</span>
        </p>
        <p class="c1"><span class="c0">Une indemnité forfaitaire pour frais de recouvrement de 40 euros par facture non réglée dans les délais prévus sera également exigible. </span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">8. Clause résolutoire</span></p>
        <p class="c1"><span class="c0">ALIENOR.NET pourra résilier immédiatement et de plein la vente ou la prestation conclue huit jours calendaires après l'envoi au client d'une lettre de mise en demeure recommandée avec accusé de réception restée en tout ou en partie sans effet pendant ce délai, et ce en cas d'inexécution d'une quelconque des clauses et conditions des présentes conditions générales de vente qui sont toutes de rigueur.</span>
        </p>
        <p class="c1"><span class="c0">En conséquent, ALIENOR.NET procèdera si bon lui semble à la suspension et/ou à l'interruption de ses prestations et/ ou ventes en cours dans le même délai. Cette disposition ne dispense pas le client du paiement de l'intégralité des sommes restant dues tant pour les prestations que pour les ventes de matériel.</span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">9. Clause de réserve de propriété</span></p>
        <p class="c1"><span class="c0">La société ALIENOR.NET conserve la propriété des biens vendus jusqu'au paiement intégral du prix, en principal et en accessoires. La société ALIENOR.NET se réserve le droit, dans les 8 jours suivant la mise en demeure prévue à l'article 7, de revendiquer le matériel vendu et/ou le support des prestations réalisées et demeurées impayées.</span>
        </p>
        <p class="c1"><span class="c0">Cette revendication, sans préjudice des règles spécifiques aux cas de redressement et liquidation judiciaire, s'effectuera par lettre recommandée avec accusé de réception, et au cas o&ugrave; le client ne défèrerait pas à la demande, les marchandises et supports de prestations pourront être obtenus au besoin par simple ordonnance de référé.</span>
        </p>
        <p class="c1"><span class="c0">Dans ce cas, les acomptes versés resteront acquis à ALIENOR.NET à titre de compensation de la jouissance de la marchandise et/ou prestation.</span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">10. Dématérialisation des factures au format PDF</span></p>
        <p class="c1"><span class="c0">La société ALIENOR.NET s'autorise le droit d'émettre - dés acception des présentes conditions et pour une durée indéterminée &ndash; des factures dématérialisées en format PDF.</span>
        </p>
        <p class="c1"><span class="c0">L'acceptation des présentes Conditions Générale de Ventes constitue l'acceptation expresse par le Client Destinataire à recevoir des factures électroniques dématérialisées de la part de l'émetteur ALIENOR.NET. L'émetteur ALIENOR.NET s'engage à envoyer ses factures au Client</span>
        </p>
        <p class="c1"><span class="c0">Destinataire au format PDF par voie électronique en tant que pièce jointe à un e-mail.</span>
        </p>
        <p class="c1"><span class="c0">L'émetteur ALIENOR.NET fournira au Client à titre gracieux un coffre d'archivage électronique accessible à partir d'un lien Internet et protégé par un Identifiant et un Mot de passe. Chaque nouvelle facture électronique sera archivée dans ce coffre électronique. Ces factures seront disponibles l'année en cours et durant les trois ans précédents ALIENOR.NET ne se substitue pas au client pour ses obligations de conservation des documents comptables.</span>
        </p>
    </div>
</div>

<p class="c1 c9"><span class="c0"></span></p>

<div class="conditions-container">
    <div class="conditions-column conditions-column-left">
        <p class="c1"><span class="c0">Le Client s'engage à envoyer à l'émetteur l'adresse e-mail du destinataire légal des factures électroniques dématérialisées.<br></span>
        </p>
        <p class="c1"><span class="c0">Le Client s'engage à informer ALIENOR.NET tout changement d'adresse e-mail de réception des factures électroniques dématérialisées. </span>
        </p>
        <p class="c1"><span class="c0">En cas d'échec de l'envoi des factures électroniques dématérialisées lié à la mise à jour de ces changements, l'émetteur ALIENOR.NET ne sera en aucun cas responsable et le Client ne sera pas exempt de son obligation de payer les factures.</span>
        </p>
        <p class="c1"><span class="c0">Les parties conviennent d'être liées légalement par les factures au format PDF dématérialisé et renoncent expressément à tout droit de remise en question de la validité des factures électroniques émises et échangées.</span>
        </p>
        <p class="c1"><span class="c0">Les &laquo; factures électroniques &raquo; sont réputées originales.</span>
        </p>
        <p class="c1"><span class="c0">En cas de non-respect des Conditions de Facturation électronique dématérialisée, notamment le non-paiement à échéance des factures électroniques, chacune des parties se réserve le droit de revenir au processus de facturation papier, avec effet immédiat, sans préavis, sans mesures judiciaires, sans préjudice de toute indemnisation pouvant être demandée à l'autre partie.</span>
        </p>

        <p class="c1"><span class="c23"></span><span class="c18">11</span><span class="c23">. </span><span class="c18">Livraison</span></p>
        <p class="c1"><span class="c0">Concernant la vente de matériel, la livraison est effectuée&nbsp;:</span>
        </p>
        <ul class="c10 lst-kix_list_18-0 start">
            <li class="c1 c14 li-bullet-0"><span class="c0">soit par remise directe de la marchandise au client</span>
            </li>
            <li class="c1 c14 li-bullet-0"><span class="c0">soit par l'envoi d'un avis de mise à disposition au client</span>
            </li>
            <li class="c1 c14 li-bullet-0"><span class="c0">soit au lieu indiqué par le client sur le bon de commande</span></li>
        </ul>
        <p class="c1"><span class="c0">Le délai de livraison indiqué lors de l'enregistrement de la commande n'est donné qu'à titre indicatif et n'est aucunement garanti.</span>
        </p>
        <p class="c1"><span class="c0">Par voie de conséquence, tout retard raisonnable dans la livraison des produits ne pourra pas donner lieu au profit du client à&nbsp;:</span>
        </p>
        <ul class="c10 lst-kix_list_18-0">
            <li class="c1 c14 li-bullet-0"><span class="c0">l'allocation de dommages et intérêts </span></li>
            <li class="c1 c14 li-bullet-0"><span class="c0">l'annulation de la commande</span></li>
        </ul>
        <p class="c1"><span class="c0">Le risque de transport est supporté en totalité par le client.</span></p>
        <p class="c1"><span class="c0">En cas de matériel manquant ou détérioré lors du transport, le client devra formuler toutes les réserves nécessaires sur le bon de commande à réception dudit matériel. Ces réserves devront être, en outre, confirmées par écrit dans les 5 jours suivant la livraison, par courrier avec accusé de réception.</span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">12. Publicité</span>
        </p>
        <p class="c1"><span class="c0">La société ALIENOR.NET est autorisée à divulguer le nom de son client en tant que référence commerciale.</span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">13. Confidentialité</span></p>
        <p class="c1"><span class="c0">Chacune des parties s'engage à ne pas divulguer les documents ou renseignements de toute nature sur l'autre partie ou toute information dont elle aurait eu connaissance. </span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">14. Données personnelles </span>
        </p>
        <p class="c1"><span class="c40 c23">Respect de la réglementation&nbsp;: </span><span class="c0">Dans le cadre de l'exécution des prestations et/des ventes chaque partie s'engage à respecter la réglementation en vigueur applicable au traitement de données à caractère personnel et, en particulier, le règlement (UE) 2016/679 du Parlement européen et du Conseil du 27 avril 2016 applicable à compter du 25 mai 2018.</span>
        </p>
        <p class="c1"><span class="c0">Le cas échéant, en tant que sous-traitant du client responsable de traitement, ALIENOR.NET s'engage notamment à traiter les données uniquement pour la ou les seule(s) finalité(s) qui fait/font l'objet de la sous-traitance ; à mettre en &oelig;uvre les mesures techniques et organisationnelles appropriées afin de protéger les données ; à traiter les données conformément aux instructions documentées du client et pour la durée de traitement convenue avec le client ; à informer le client si en cas d'instruction qui constitue une violation de la réglementation sur la protection des données ; à informer le client s'il est tenu de transfert de données vers un pays tiers ou à une organisation internationale, en vertu du droit de l'Union ou du droit de l'Etat membre auquel il est soumis ; à garantir la confidentialité des données à caractère personnel traitées ; à veiller à ce que les personnes autorisées à traiter les données à caractère personnel s'engagent à respecter la confidentialité ou soient soumises à une obligation de confidentialité contractuelle.</span>
        </p>
    </div>

    <div class="conditions-column">
        <p class="c1"><span class="c0">obligation légale appropriée de confidentialité et re&ccedil;oivent la formation nécessaire en matière de protection des données à caractère personnel ; à prendre en compte, s'agissant de ses outils, produits, applications ou services, les principes de&nbsp;protection des données dès la conception&nbsp;et de&nbsp;protection des données par défaut.</span>
        </p>
        <p class="c17"><span class="c40 c23">Droit des personnes</span><span class="c0">&nbsp;: Sauf dispositions contraires convenues entre les parties, il appartient au client en sa qualité de responsable de traitement de fournir aux personnes concernées par les opérations de traitement et au moment de la collecte des données toutes les informations prévues par le règlement européen. Les demandes d'exercice des droits des personnes concernées et notamment le droit d'accès, de rectification, d'effacement et d'opposition, droit à la limitation du traitement s'exercent directement auprès du client. Toute demande ayant un impact sur les prestations fournies par ALIENOR.NET et sur les traitements réalisés devra être immédiatement transmise par le client à ALIENOR.NET. Dans la mesure du possible, ALIENOR.NET aidera le client à s'acquitter de son obligation de donner suite à ces demandes. Cette prestation pourra faire l'objet d'une facturation éventuelle.</span>
        </p>
        <p class="c1"><span class="c23 c40">Sous-traitant&nbsp;: </span><span class="c0">De convention expresse entre les parties, ALIENOR.NET peut faire appel à un sous-traitant ultérieur pour mener des activités de traitement spécifiques lequel sera tenu des mêmes obligations que celles d'ALIENOR.NET. ALIENOR.NET informera préalablement et par</span>
        </p>
        <p class="c1"><span class="c0">écrit le client de tout changement envisagé concernant la sous-traitance ultérieure. Ce dernier disposera d'un délai minium de 15 jours à compter de la date de réception de cette information pour présenter ses objections. Pendant toute la durée de la convention ou du contrat conclu entre les parties, le client s'interdit de solliciter directement le ou les sous-traitants d'ALIENOR.NET.</span>
        </p>
        <p class="c1"><span class="c40 c23">Sort des données&nbsp;: </span><span class="c0">Au terme de ses prestations, ALIENOR.NET s'engage à&nbsp;:</span>
        </p>
        <ul class="c10 lst-kix_list_19-0 start">
            <li class="c1 c51 li-bullet-3"><span class="c0">Détruire toutes les données à caractère personnel traitées pour le compte du client, étant précisé que ceci ne préjudicie pas aux droits ou aux obligations de chaque partie de procéder à l'archivage des données dans les conditions autorisées par la réglementation applicable,</span>
            </li>
            <li class="c1 c51 li-bullet-4"><span class="c0">Et, sur demande du client, à renvoyer toutes les données à caractère personnel au client ou au nouveau sous-traitant désigné par le client, étant précisé que ce renvoi pourra faire l'objet &ndash; en fonction des prestations à réaliser par ALIENOR.NET &ndash; d'une facturation spécifique pour laquelle un devis préalable sera communiqué au client.</span>
            </li>
        </ul>
        <p class="c1"><span class="c0">Ceci ne préjudicie pas aux droits ou aux obligations d'ALIENOR.NET de procéder à l'archivage des données dans les conditions autorisées par la réglementation applicable.</span>
        </p>
        <p class="c1"><span class="c40 c23">Délégué à la protection des données&nbsp;: </span><span class="c0">Chaque partie communique à l'autre&nbsp;le nom et les coordonnées de son délégué à la protection des données, si elle en a désigné un conformément à l'article 37 du Règlement européen sur la protection des données.</span>
        </p>
        <p class="c1"><span class="c23">Le délégué à la protection des données pour ALIENOR.NET est Alexia BARRAL &ndash; 05 56 56 46 17 &ndash; </span><span class="c23 c56"><a class="c29" href="mailto:<EMAIL>"><EMAIL></a></span><span class="c0">. </span></p>
        <p class="c1"><span class="c0">Les présentes dispositions générales sont le cas échéant complétées par des conditions particulières précisant les obligations des parties.</span>
        </p>

        <p class="c1"><span class="c3">15. Force Majeure</span>
        </p>
        <p class="c1"><span class="c0">La responsabilité de la société ALIENOR.NET ne pourra pas être mise en &oelig;uvre si la non-exécution ou le retard dans l'exécution de l'une de ses obligations décrites dans les présentes conditions générales de vente découle d'un cas de force majeure.</span>
        </p>
        <p class="c1"><span class="c0">A ce titre, la force majeure s'entend de tout événement échappant au contr&ocirc;le du débiteur, qui ne pouvait être raisonnablement prévu lors de la conclusion du contrat et dont les effets ne peuvent être évités par des mesures appropriées au sens de l'article 1218 du Code Civil.</span>
        </p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1 c9"><span class="c0"></span></p>
        <p class="c1"><span class="c3">16. Tribunaux compétents et loi applicable</span>
        </p>
        <p class="c1"><span class="c0">Tout litige est soumis au droit fran&ccedil;ais. A défaut de résolution amiable, le litige sera porté devant les tribunaux de Bordeaux.</span>
        </p>
    </div>
</div>

<p class="c1 c9"><span class="c12"></span></p>
<p class="c38 c9"><span class="c8"></span></p></body>
</html>